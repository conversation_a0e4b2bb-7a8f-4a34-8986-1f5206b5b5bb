import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Box,
  AppBar,
  Toolbar,
  IconButton,
  Typography,
  useTheme,
  useMediaQuery
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import MaterialSidebar from '../components/MaterialSidebar';
import MaterialAdminDashboard from './MaterialAdminDashboard';
import MaterialTrackUpload from '../components/MaterialTrackUpload';
import MaterialReports from '../components/MaterialReports';
import MaterialTrackLibrary from '../components/MaterialTrackLibrary';
import MaterialStoreManagement from '../components/MaterialStoreManagement';
import MaterialMonitoring from '../components/MaterialMonitoring';
import MaterialAdminPlaylists from '../components/MaterialAdminPlaylists';
import MaterialAdminRadioStations from '../components/MaterialAdminRadioStations';
import MaterialTrending from '../components/MaterialTrending';
import MaterialMessages from '../components/MaterialMessages';
import MaterialAccountManagement from '../components/MaterialAccountManagement';
import MaterialAdvancedAnalytics from '../components/MaterialAdvancedAnalytics';

import MaterialSystemSettings from '../components/MaterialSystemSettings';
import MaterialComplianceActivityMonitor from '../components/MaterialComplianceActivityMonitor';
import VenueLicenseManagement from '../components/VenueLicenseManagement';

const AdminDashboard = () => {
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'analytics');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // For the main dashboard view (analytics tab), use the new Material UI design
  if (activeTab === 'analytics') {
    return <MaterialAdminDashboard />;
  }

  // For other tabs, use consistent Material UI layout
  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      width: '100%',
      position: 'relative'
    }}>
      {/* Sidebar */}
      <MaterialSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      {/* Main Content */}
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          minWidth: 0, // Prevent flex item from overflowing
          overflow: 'hidden', // Prevent content from overflowing
          width: '100%',
          maxWidth: '100%'
        }}
      >
        {/* Mobile Header */}
        {isMobile && (
          <AppBar position="static" sx={{ bgcolor: 'background.paper' }}>
            <Toolbar>
              <IconButton
                edge="start"
                color="inherit"
                onClick={() => setSidebarOpen(true)}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Admin Dashboard
              </Typography>
            </Toolbar>
          </AppBar>
        )}

        {/* Content Area */}
        <Box sx={{ p: 3, flexGrow: 1, minWidth: 0, overflow: 'auto' }}>
          {/* Desktop Header */}
          

          {/* Tab Content */}
          <Box sx={{ mt: 2 }}>
            {activeTab === 'uploads' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialTrackUpload />
              </motion.div>
            )}

            {activeTab === 'reports' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialReports />
              </motion.div>
            )}

            {activeTab === 'stores' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialStoreManagement />
              </motion.div>
            )}

            {activeTab === 'tracks' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialTrackLibrary />
              </motion.div>
            )}

            {activeTab === 'monitoring' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialMonitoring />
              </motion.div>
            )}

            {activeTab === 'playlists' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialAdminPlaylists />
              </motion.div>
            )}

            {activeTab === 'radio' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialAdminRadioStations />
              </motion.div>
            )}



            {activeTab === 'trending' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialTrending />
              </motion.div>
            )}



            {activeTab === 'messages' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialMessages />
              </motion.div>
            )}

            {activeTab === 'account' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialAccountManagement />
              </motion.div>
            )}

            {activeTab === 'settings' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialSystemSettings />
              </motion.div>
            )}

            {activeTab === 'compliance' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialComplianceActivityMonitor />
              </motion.div>
            )}

            {activeTab === 'analysis' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <MaterialAdvancedAnalytics />
              </motion.div>
            )}



            {activeTab === 'licenses' && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <VenueLicenseManagement />
              </motion.div>
            )}


          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default AdminDashboard;
