// Test script to check analytics API endpoints
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

// Test function to check API endpoints
async function testAnalyticsEndpoints() {
  console.log('Testing Analytics API Endpoints...\n');

  // Test endpoints without authentication first
  const endpoints = [
    '/admin/analytics/plays-by-time?timeRange=week',
    '/admin/analytics/genre-analytics?timeRange=week',
    '/admin/analytics/store-performance?timeRange=week',
    '/admin/analytics/track-popularity?timeRange=week',
    '/admin/analytics/user-engagement?timeRange=week'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`Testing: ${endpoint}`);
      const response = await axios.get(`${API_BASE_URL}${endpoint}`, {
        timeout: 5000
      });
      console.log(`✅ Status: ${response.status}`);
      console.log(`✅ Data length: ${response.data?.data?.length || 'N/A'}`);
      console.log(`✅ Response: ${JSON.stringify(response.data).substring(0, 200)}...\n`);
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      console.log(`❌ Status: ${error.response?.status || 'No response'}`);
      console.log(`❌ Data: ${JSON.stringify(error.response?.data || {}).substring(0, 200)}\n`);
    }
  }

  // Test basic connectivity
  try {
    console.log('Testing basic server connectivity...');
    const response = await axios.get(`${API_BASE_URL.replace('/api', '')}/`, {
      timeout: 5000
    });
    console.log(`✅ Server is responding: ${response.status}`);
  } catch (error) {
    console.log(`❌ Server connectivity error: ${error.message}`);
  }

  // Test if we can reach the API base
  try {
    console.log('Testing API base path...');
    const response = await axios.get(`${API_BASE_URL}/`, {
      timeout: 5000
    });
    console.log(`✅ API base is responding: ${response.status}`);
  } catch (error) {
    console.log(`❌ API base error: ${error.message}`);
    console.log(`❌ Status: ${error.response?.status || 'No response'}`);
  }
}

// Run the test
testAnalyticsEndpoints().catch(console.error);
