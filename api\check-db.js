const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import models
const User = require('./models/User.model');
const PlayHistory = require('./models/PlayHistory.model');
const Track = require('./models/Track.model');
const Store = require('./models/Store.model');

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('MongoDB connected for checking');
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

const checkDatabase = async () => {
  try {
    await connectDB();

    console.log('=== Database Status Check ===\n');

    // Check users
    const userCount = await User.countDocuments();
    console.log(`Users: ${userCount}`);
    
    if (userCount > 0) {
      const adminUser = await User.findOne({ role: 'admin' });
      console.log(`Admin user exists: ${adminUser ? 'Yes' : 'No'}`);
      if (adminUser) {
        console.log(`Admin username: ${adminUser.username}`);
      }
    }

    // Check stores
    const storeCount = await Store.countDocuments();
    console.log(`Stores: ${storeCount}`);

    // Check tracks
    const trackCount = await Track.countDocuments();
    console.log(`Tracks: ${trackCount}`);

    // Check play history
    const playHistoryCount = await PlayHistory.countDocuments();
    console.log(`Play History Records: ${playHistoryCount}`);

    if (playHistoryCount > 0) {
      const recentPlay = await PlayHistory.findOne().sort({ playedDate: -1 });
      console.log(`Most recent play: ${recentPlay ? recentPlay.playedDate : 'None'}`);
    }

    console.log('\n=== Recommendations ===');
    if (userCount === 0) {
      console.log('❌ No users found. Run: npm run seed');
    } else if (!await User.findOne({ role: 'admin' })) {
      console.log('❌ No admin user found. Run: npm run seed');
    } else {
      console.log('✅ Database appears to be seeded');
      console.log('✅ Try logging in with:');
      console.log('   Username: admin');
      console.log('   Password: admin123');
    }

  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    mongoose.connection.close();
  }
};

checkDatabase();
