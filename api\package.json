{"name": "api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "seed": "node scripts/seed.js", "seed:compliance": "node scripts/seed-compliance-users.js", "seed:analytics": "node scripts/quick-seed-analytics.js", "clear:analytics": "node scripts/clear-analytics.js", "init:settings": "node scripts/initSettings.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "cron": "^4.3.0", "csv-writer": "^1.6.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "morgan": "^1.10.0", "multer": "^2.0.0", "nodemailer": "^6.9.8", "pdfmake": "^0.2.20", "uuid": "^11.1.0", "xml2js": "^0.6.2"}}