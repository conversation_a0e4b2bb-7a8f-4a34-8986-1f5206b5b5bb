import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  TextField,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tabs,
  Tab,
  Divider
} from '@mui/material';
import {
  QueueMusic as PlaylistIcon,
  Add as AddIcon,
  Search as SearchIcon,
  PlayArrow as PlayArrowIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MusicNote as MusicNoteIcon,

  Schedule as ScheduleIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { playlistService, adminService, trackService } from '../services/api';
import { useMedia } from '../context/MediaContext';

const MaterialAdminPlaylists = () => {
  const { playMusic } = useMedia();
  const [playlists, setPlaylists] = useState([]);
  const [stores, setStores] = useState([]);
  const [tracks, setTracks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedPlaylist, setSelectedPlaylist] = useState(null);
  const [createForm, setCreateForm] = useState({
    name: '',
    description: '',
    storeIds: []
  });
  const [selectedTracks, setSelectedTracks] = useState([]);
  const [trackSearchTerm, setTrackSearchTerm] = useState('');
  const [dialogTab, setDialogTab] = useState(0); // 0 = Info, 1 = Tracks

  useEffect(() => {
    loadData();
    loadTracks();
  }, []);

  const loadTracks = async () => {
    try {
      const response = await trackService.getAll();
      const tracksData = response.data?.data || response.data || [];
      setTracks(tracksData);
    } catch (error) {
      console.error('Failed to load tracks:', error);
    }
  };

  const loadData = async () => {
    setLoading(true);
    try {
      const [playlistsRes, storesRes] = await Promise.all([
        playlistService.getAll(),
        adminService.getAllStores()
      ]);

      // API returns data directly, not wrapped in .data
      const playlistsData = playlistsRes.data || playlistsRes;
      const storesData = storesRes.data || storesRes;

      // Transform playlists to include calculated fields using real data
      const transformedPlaylists = playlistsData.map(playlist => ({
        ...playlist,
        trackCount: playlist.tracks?.length || 0,
        duration: calculateDuration(playlist.tracks),
        status: calculatePlaylistStatus(playlist),
        stores: getPlaylistStores(playlist, storesData),
        // Ensure we have real timestamps
        createdAt: playlist.createdAt || new Date().toISOString(),
        updatedAt: playlist.updatedAt || playlist.createdAt || new Date().toISOString()
      }));

      setPlaylists(transformedPlaylists);
      setStores(storesData);
    } catch (error) {
      console.error('Failed to load data:', error);
      // Show error state instead of empty data
      setPlaylists([]);
      setStores([]);
    } finally {
      setLoading(false);
    }
  };

  const calculateDuration = (tracks) => {
    if (!tracks || tracks.length === 0) return '0m';
    const totalSeconds = tracks.reduce((sum, track) => sum + (track.duration || 0), 0);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
  };

  const calculatePlaylistStatus = (playlist) => {
    if (!playlist.schedule || playlist.schedule.length === 0) {
      return 'active'; // Default to active if no schedule
    }

    const now = new Date();
    const currentDay = now.toLocaleString('en-US', { weekday: 'long' });
    const currentTime = now.toTimeString().slice(0, 5); // HH:mm

    // Check if playlist is currently scheduled
    const isActive = playlist.schedule.some(schedule => {
      return schedule.day === currentDay &&
             schedule.startTime <= currentTime &&
             schedule.endTime >= currentTime;
    });

    return isActive ? 'active' : 'scheduled';
  };

  const getPlaylistStores = (playlist, stores) => {
    if (!playlist.storeId) {
      return ['All Stores'];
    }

    // Handle both populated and non-populated storeId
    if (typeof playlist.storeId === 'object' && playlist.storeId.name) {
      return [playlist.storeId.name];
    } else if (typeof playlist.storeId === 'string') {
      const store = stores.find(s => s._id === playlist.storeId);
      return store ? [store.name] : ['Unknown Store'];
    }

    return ['Unknown Store'];
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'draft': return 'warning';
      case 'scheduled': return 'info';
      case 'inactive': return 'error';
      default: return 'default';
    }
  };

  const handleViewPlaylist = (playlist) => {
    setSelectedPlaylist(playlist);
    setDialogOpen(true);
  };

  const handleEditPlaylist = (playlist) => {
    setSelectedPlaylist(playlist);
    setCreateForm({
      name: playlist.name,
      description: playlist.description || '',
      storeIds: playlist.storeId ? [playlist.storeId] : []
    });
    // Initialize selected tracks with current playlist tracks
    const currentTrackIds = playlist.tracks ? playlist.tracks.map(track => track._id || track) : [];
    setSelectedTracks(currentTrackIds);
    setDialogOpen(true);
  };

  const handlePlayPlaylist = async (playlist) => {
    try {
      // If playlist has tracks, play them
      if (playlist.tracks && playlist.tracks.length > 0) {
        // Use MediaContext's playMusic function to properly integrate with the persistent player
        // Pass playlist metadata for proper tracking and display
        const playlistInfo = {
          _id: playlist._id,
          name: playlist.name
        };
        playMusic(playlist.tracks, 0, playlistInfo);
        console.log(`Playing playlist: ${playlist.name} with ${playlist.tracks.length} tracks`);
      } else {
        console.warn('Playlist has no tracks to play');
      }
    } catch (error) {
      console.error('Failed to play playlist:', error);
    }
  };

  const handleDeletePlaylist = async (id) => {
    try {
      await playlistService.delete(id);
      loadData(); // Reload data after deletion
    } catch (error) {
      console.error('Failed to delete playlist:', error);
    }
  };

  const handleCreatePlaylist = async () => {
    try {
      if (selectedPlaylist) {
        // Edit existing playlist - update both metadata and tracks
        await playlistService.update(selectedPlaylist._id, {
          name: createForm.name,
          description: createForm.description,
          trackIds: selectedTracks
        });
      } else {
        // Create new playlist for each selected store, or one global playlist if none selected
        const storeIds = createForm.storeIds.length > 0 ? createForm.storeIds : [null];

        for (const storeId of storeIds) {
          await playlistService.create({
            name: createForm.name,
            description: createForm.description,
            storeId: storeId,
            tracks: selectedTracks
          });
        }
      }

      setCreateForm({ name: '', description: '', storeIds: [] });
      setSelectedTracks([]);
      setDialogOpen(false);
      setSelectedPlaylist(null);
      loadData(); // Reload data after creation/update
    } catch (error) {
      console.error('Failed to save playlist:', error);
    }
  };

  const handleTrackToggle = (trackId) => {
    setSelectedTracks(prev =>
      prev.includes(trackId)
        ? prev.filter(id => id !== trackId)
        : [...prev, trackId]
    );
  };

  const filteredTracks = tracks.filter(track =>
    track.title.toLowerCase().includes(trackSearchTerm.toLowerCase()) ||
    track.artist.toLowerCase().includes(trackSearchTerm.toLowerCase())
  );

  const filteredPlaylists = playlists.filter(playlist => {
    const matchesSearch = playlist.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || playlist.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const totalPlaylists = playlists.length;
  const activePlaylists = playlists.filter(p => p.status === 'active').length;
  const totalTracks = playlists.reduce((sum, p) => sum + p.trackCount, 0);
  const totalDuration = playlists.reduce((sum, p) => {
    const duration = p.duration.split('h ');
    const hours = parseInt(duration[0]) || 0;
    const minutes = parseInt(duration[1]) || 0;
    return sum + (hours * 60) + minutes;
  }, 0);

  return (
    <Box sx={{
      p: 3,
      width: '100%',
      maxWidth: '100%',
      overflow: 'hidden',
      minWidth: 0,
      boxSizing: 'border-box'
    }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
            Playlist Management
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            setSelectedPlaylist(null);
            setCreateForm({ name: '', description: '', storeIds: [] });
            setDialogOpen(true);
          }}
        >
          Create Playlist
        </Button>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PlaylistIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Playlists</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                {totalPlaylists}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                All playlists
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <PlayArrowIcon color="success" sx={{ mr: 1 }} />
                <Typography variant="h6">Active</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                {activePlaylists}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Currently playing
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <MusicNoteIcon color="secondary" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Tracks</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                {totalTracks}
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Across all playlists
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <ScheduleIcon color="info" sx={{ mr: 1 }} />
                <Typography variant="h6">Total Duration</Typography>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 600 }}>
                {Math.floor(totalDuration / 60)}h {totalDuration % 60}m
              </Typography>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Combined duration
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search playlists..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={3}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Status"
              >
                <MenuItem value="all">All Status</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="draft">Draft</MenuItem>
                <MenuItem value="scheduled">Scheduled</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={3}>
            <Button
              fullWidth
              variant="outlined"
              onClick={loadData}
              disabled={loading}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Playlists Table */}
      <Paper sx={{ borderRadius: 3, overflow: 'hidden' }}>
        <Box sx={{ p: 3, pb: 0 }}>
          <Typography variant="h6" sx={{ mb: 3 }}>
            All Playlists ({filteredPlaylists.length})
          </Typography>
        </Box>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ bgcolor: 'grey.50' }}>
                <TableCell sx={{ fontWeight: 600, color:'black' }}>Playlist</TableCell>
                <TableCell sx={{ fontWeight: 600, color:'black' }}>Tracks</TableCell>
                <TableCell sx={{ fontWeight: 600, color:'black', display: { xs: 'none', sm: 'table-cell' } }}>Duration</TableCell>
                <TableCell sx={{ fontWeight: 600, color:'black', display: { xs: 'none', md: 'table-cell' } }}>Stores</TableCell>
                <TableCell sx={{ fontWeight: 600, color:'black' }}>Status</TableCell>
                <TableCell sx={{ fontWeight: 600, color:'black', display: { xs: 'none', lg: 'table-cell' } }}>Created By</TableCell>
                <TableCell sx={{ fontWeight: 600, color:'black', display: { xs: 'none', lg: 'table-cell' } }}>Last Modified</TableCell>
                <TableCell sx={{ fontWeight: 600, color:'black' }}>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredPlaylists.map((playlist) => (
                <TableRow
                  key={playlist._id}
                  sx={{
                    '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.02)' }
                  }}
                >
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar sx={{ bgcolor: 'primary.main', width: 40, height: 40 }}>
                        <PlaylistIcon />
                      </Avatar>
                      <Box sx={{ minWidth: 0, flex: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 600 }} noWrap>
                          {playlist.name}
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
                          Created {new Date(playlist.createdAt || Date.now()).toLocaleDateString()}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      {playlist.trackCount}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>
                    <Typography variant="body2">
                      {playlist.duration}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ display: { xs: 'none', md: 'table-cell' } }}>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }} noWrap>
                      {playlist.stores.join(', ')}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={playlist.status}
                      size="small"
                      color={getStatusColor(playlist.status)}
                      sx={{ textTransform: 'capitalize' }}
                    />
                  </TableCell>
                  <TableCell sx={{ display: { xs: 'none', lg: 'table-cell' } }}>
                    <Typography variant="body2">
                      {playlist.createdBy?.username || 'Unknown'}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ display: { xs: 'none', lg: 'table-cell' } }}>
                    <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                      {new Date(playlist.updatedAt || playlist.createdAt || Date.now()).toLocaleDateString()}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                      {/* Desktop Actions */}
                      <Box sx={{ display: { xs: 'none', sm: 'flex' }, gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewPlaylist(playlist)}
                          sx={{ bgcolor: 'grey.100', color:'black', '&:hover': { bgcolor: 'grey.200' } }}
                        >
                          <ViewIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleEditPlaylist(playlist)}
                          sx={{ bgcolor: 'grey.100', color:'blue', '&:hover': { bgcolor: 'grey.200' } }}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handlePlayPlaylist(playlist)}
                          sx={{ bgcolor: 'grey.100', color:'green', '&:hover': { bgcolor: 'grey.200' } }}
                        >
                          <PlayArrowIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDeletePlaylist(playlist._id)}
                          sx={{
                            bgcolor: 'grey.100',
                            color: 'error.main',
                            '&:hover': { bgcolor: 'error.50' }
                          }}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>

                      {/* Mobile Actions - Only View button */}
                      <Box sx={{ display: { xs: 'flex', sm: 'none' } }}>
                        <IconButton
                          size="small"
                          onClick={() => handleViewPlaylist(playlist)}
                          sx={{ bgcolor: 'grey.100', '&:hover': { bgcolor: 'grey.200' } }}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Box>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* View/Edit Playlist Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {selectedPlaylist && createForm.name === selectedPlaylist.name ?
            `Edit Playlist: ${selectedPlaylist.name}` :
            selectedPlaylist ? `Playlist: ${selectedPlaylist.name}` :
            'Create New Playlist'}
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {/* Tabs for Edit/Create mode */}
          {(selectedPlaylist && createForm.name === selectedPlaylist.name) || !selectedPlaylist ? (
            <>
              <Tabs value={dialogTab} onChange={(e, newValue) => setDialogTab(newValue)} sx={{ px: 3, pt: 2 }}>
                <Tab label="Playlist Info" />
                <Tab label="Tracks" />
              </Tabs>
              <Divider />

              {/* Tab Content */}
              <Box sx={{ p: 3 }}>
                {dialogTab === 0 ? (
                  // Playlist Info Tab
                  <Box>
                    <TextField
                      fullWidth
                      label="Playlist Name"
                      value={createForm.name}
                      onChange={(e) => setCreateForm({ ...createForm, name: e.target.value })}
                      sx={{ mb: 2 }}
                    />
                    <TextField
                      fullWidth
                      label="Description"
                      value={createForm.description}
                      onChange={(e) => setCreateForm({ ...createForm, description: e.target.value })}
                      multiline
                      rows={3}
                      sx={{ mb: 2 }}
                    />
                    {!selectedPlaylist && (
                      <FormControl fullWidth sx={{ mb: 2 }}>
                        <InputLabel>Target Stores</InputLabel>
                        <Select
                          multiple
                          value={createForm.storeIds}
                          onChange={(e) => setCreateForm({ ...createForm, storeIds: e.target.value })}
                        >
                          {stores.map((store) => (
                            <MenuItem key={store._id} value={store._id}>
                              {store.name}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    )}
                  </Box>
                ) : (
                  // Tracks Tab
                  <Box>
                    <TextField
                      fullWidth
                      placeholder="Search tracks..."
                      value={trackSearchTerm}
                      onChange={(e) => setTrackSearchTerm(e.target.value)}
                      sx={{ mb: 2 }}
                    />
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      Selected: {selectedTracks.length} tracks
                    </Typography>
                    <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                      <List>
                        {filteredTracks.map((track) => (
                          <ListItem key={track._id} dense>
                            <ListItemIcon>
                              <Checkbox
                                checked={selectedTracks.includes(track._id)}
                                onChange={() => handleTrackToggle(track._id)}
                              />
                            </ListItemIcon>
                            <ListItemText
                              primary={track.title}
                              secondary={`${track.artist} - ${track.album || 'Unknown Album'}`}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  </Box>
                )}
              </Box>
            </>
          ) : (
            // View mode for existing playlist
            <Box sx={{ p: 3 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                <strong>Tracks:</strong> {selectedPlaylist.trackCount}
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                <strong>Duration:</strong> {selectedPlaylist.duration}
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                <strong>Stores:</strong> {selectedPlaylist.stores.join(', ')}
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                <strong>Status:</strong>
                <Chip
                  label={selectedPlaylist.status}
                  size="small"
                  color={getStatusColor(selectedPlaylist.status)}
                  sx={{ ml: 1, textTransform: 'capitalize' }}
                />
              </Typography>
              <Typography variant="body1" sx={{ mb: 2 }}>
                <strong>Created by:</strong> {selectedPlaylist.createdBy?.username || 'Unknown'}
              </Typography>

              {/* Display actual tracks */}
              {selectedPlaylist.tracks && selectedPlaylist.tracks.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Tracks in Playlist
                  </Typography>
                  <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                    <List>
                      {selectedPlaylist.tracks.map((track, index) => (
                        <ListItem key={track._id || index} divider>
                          <ListItemText
                            primary={`${index + 1}. ${track.title}`}
                            secondary={`${track.artist} - ${track.album || 'Unknown Album'} (${Math.floor(track.duration / 60)}:${String(track.duration % 60).padStart(2, '0')})`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setDialogOpen(false);
            setSelectedPlaylist(null);
            setCreateForm({ name: '', description: '', storeIds: [] });
            setSelectedTracks([]);
            setDialogTab(0);
            setTrackSearchTerm('');
          }}>
            {selectedPlaylist && createForm.name === selectedPlaylist.name ? 'Cancel' : 'Close'}
          </Button>
          {(selectedPlaylist && createForm.name === selectedPlaylist.name) || !selectedPlaylist ? (
            <Button
              variant="contained"
              onClick={handleCreatePlaylist}
              disabled={!createForm.name.trim()}
            >
              {selectedPlaylist ? 'Save Changes' : 'Create Playlist'}
            </Button>
          ) : null}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MaterialAdminPlaylists;
