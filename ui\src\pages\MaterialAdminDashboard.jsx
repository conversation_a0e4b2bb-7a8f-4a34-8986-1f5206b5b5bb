import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useSearchParams } from 'react-router-dom';
import { adminService, historyService } from '../services/api';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  Chip,
  IconButton,
  AppBar,
  Toolbar,
  useMediaQuery,
  useTheme,
  Tabs,
  Tab
} from '@mui/material';
import MusicNoteIcon from '@mui/icons-material/MusicNote';
import NotificationsIcon from '@mui/icons-material/Notifications';
import AddIcon from '@mui/icons-material/Add';
import MenuIcon from '@mui/icons-material/Menu';
import MaterialSidebar from '../components/MaterialSidebar';
import MaterialMetricCard from '../components/MaterialMetricCard';



const MaterialAdminDashboard = () => {
  const { user } = useAuth();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'analytics');
  const [analyticsSubTab, setAnalyticsSubTab] = useState('overview');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [topTracks, setTopTracks] = useState([]);
  const [storeSummary, setStoreSummary] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('lg'));

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      console.log('Loading dashboard data...');
      const [topTracksRes, storeSummaryRes, historyRes] = await Promise.all([
        adminService.getTopTracks({ limit: 10, timeRange: 'week' }),
        adminService.getStoreSummary({ timeRange: 'week' }),
        historyService.get({ limit: 10, sortBy: 'playedDate', sortOrder: 'desc' })
      ]);

      console.log('Dashboard data loaded successfully:', {
        topTracks: topTracksRes.data,
        storeSummary: storeSummaryRes.data,
        recentActivity: historyRes.data
      });

      // Handle the new API response structure
      const topTracksData = topTracksRes.data?.success ? topTracksRes.data.data : topTracksRes.data;
      const storeSummaryData = storeSummaryRes.data?.success ? storeSummaryRes.data.data : storeSummaryRes.data;
      const historyData = historyRes.data?.success ? historyRes.data.data : historyRes.data;

      setTopTracks(Array.isArray(topTracksData) ? topTracksData : []);
      setStoreSummary(Array.isArray(storeSummaryData) ? storeSummaryData : []);
      setRecentActivity(Array.isArray(historyData) ? historyData : []);

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      console.error('Error details:', error.response?.data || error.message);

      // Show error message to user instead of fallback data
      setTopTracks([]);
      setStoreSummary([]);
      setRecentActivity([]);

      // You could add a toast notification here
      console.warn('Dashboard data could not be loaded. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  // Calculate metrics from real data
  const totalPlays = recentActivity.length > 0 ? recentActivity.reduce((sum, activity) => sum + (activity.playCount || 1), 0) : 0;
  const totalStores = storeSummary.length;
  const totalTracks = topTracks.length;

  const metricsData = [
    {
      title: 'Track Plays',
      value: totalPlays > 1000 ? `${(totalPlays / 1000).toFixed(1)}k` : totalPlays.toString(),
      subtitle: 'TOTAL PLAYS',
      trend: '+12%',
      trendColor: 'primary',
      chartData: [24, 32, 28, 40, 39, 48, 56, 73, 100],
      color: 'primary'
    },
    {
      title: 'Active Stores',
      value: totalStores.toString(),
      subtitle: 'STORES ONLINE',
      trend: '+8%',
      trendColor: 'success',
      chartData: [24, 35, 29, 47, 46, 59, 71, 84, 100],
      color: 'success'
    },
    {
      title: 'Total Tracks',
      value: totalTracks.toString(),
      subtitle: 'IN LIBRARY',
      trend: '+15%',
      trendColor: 'info',
      chartData: [38, 48, 43, 57, 56, 67, 76, 87, 100],
      color: 'info'
    }
  ];

  // Use real store data for table
  const tableData = storeSummary.map((store, index) => ({
    id: store._id || index,
    distribution: store.storeName || 'Unknown Store',
    store: store.storeAddress || 'N/A',
    plays: store.totalPlays ? `${store.totalPlays}` : '0',
    playlists: store.playlistCount || '0',
    tracks: store.trackCount || '0',
    status: store.isActive ? 'Active' : 'Inactive'
  }));

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      width: '100%',
      position: 'relative'
    }}>
      {/* Sidebar */}
      <MaterialSidebar isOpen={sidebarOpen} setIsOpen={setSidebarOpen} />

      {/* Main Content */}
      <Box
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          minWidth: 0, // Prevent flex item from overflowing
          overflow: 'hidden', // Prevent content from overflowing
          width: '100%',
          maxWidth: '100%'
        }}
      >
        {/* Mobile Header */}
        {isMobile && (
          <AppBar position="static" sx={{ bgcolor: 'background.paper' }}>
            <Toolbar>
              <IconButton
                edge="start"
                color="inherit"
                onClick={() => setSidebarOpen(true)}
                sx={{ mr: 2 }}
              >
                <MenuIcon />
              </IconButton>
              <Typography variant="h6" sx={{ flexGrow: 1 }}>
                Admin Dashboard
              </Typography>
            </Toolbar>
          </AppBar>
        )}

        {/* Content Area */}
        <Box sx={{ p: 3, flexGrow: 1, minWidth: 0, overflow: 'auto' }}>


          {/* Tab Content */}
          {analyticsSubTab === 'overview' && (
            <>
              {/* Metrics Cards */}
              <Grid container spacing={3} sx={{ mb: 4 }}>
                {metricsData.map((metric, index) => (
                  <Grid item xs={12} md={4} key={index}>
                    <MaterialMetricCard {...metric} />
                  </Grid>
                ))}
              </Grid>

              {/* Music Stores Chart */}
              <Paper sx={{ p: 3, mb: 4, bgcolor: 'background.paper', borderRadius: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                Music Stores
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  Management
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  size="small"
                  sx={{ textTransform: 'none' }}
                >
                  Delegate
                </Button>
              </Box>
            </Box>

            {/* Store Activity Chart */}
            <Box sx={{ height: 256, mb: 3, p: 2, bgcolor: 'background.default', borderRadius: 2 }}>
              {storeSummary.length > 0 ? (
                <Box sx={{ display: 'flex', alignItems: 'end', gap: 3, height: '100%', px: 2 }}>
                  {storeSummary.slice(0, 6).map((store, index) => {
                    const maxPlays = Math.max(...storeSummary.map(s => s.totalPlays || 0));
                    const height = maxPlays > 0 ? ((store.totalPlays || 0) / maxPlays) * 180 : 20;
                    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'];

                    return (
                      <Box key={index} sx={{ flex: 1, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                        <Box
                          sx={{
                            width: '100%',
                            maxWidth: 50,
                            height: height,
                            bgcolor: colors[index % colors.length],
                            borderRadius: 1,
                            mb: 2,
                            display: 'flex',
                            alignItems: 'end',
                            justifyContent: 'center',
                            pb: 1,
                            transition: 'all 0.3s ease',
                            position: 'relative',
                            '&:hover': {
                              transform: 'scale(1.05)',
                              boxShadow: 3,
                              '&::after': {
                                content: `"${store.storeName}"`,
                                position: 'absolute',
                                top: -30,
                                left: '50%',
                                transform: 'translateX(-50%)',
                                bgcolor: 'background.paper',
                                color: 'text.primary',
                                px: 1,
                                py: 0.5,
                                borderRadius: 1,
                                fontSize: 12,
                                whiteSpace: 'nowrap',
                                boxShadow: 2,
                                zIndex: 10
                              }
                            }
                          }}
                        >
                          <Typography variant="caption" sx={{ color: 'white', fontWeight: 600, fontSize: 11 }}>
                            {store.totalPlays || 0}
                          </Typography>
                        </Box>
                        <Typography
                          variant="caption"
                          sx={{
                            color: 'text.secondary',
                            textAlign: 'center',
                            fontSize: 10,
                            maxWidth: 70,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {store.storeName?.split(' ')[0] || 'Store'}
                        </Typography>
                      </Box>
                    );
                  })}
                </Box>
              ) : (
                <Box
                  sx={{
                    height: '100%',
                    bgcolor: 'grey.100',
                    borderRadius: 2,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 2
                  }}
                >
                  <Typography variant="h6" sx={{ color: 'text.secondary' }}>
                    📊
                  </Typography>
                  <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                    {loading ? 'Loading store data...' : 'No store data available'}
                  </Typography>
                </Box>
              )}
                </Box>
              </Paper>

              {/* Music Stores Table */}
              <Paper sx={{ bgcolor: 'background.paper', borderRadius: 3, overflow: 'hidden' }}>
                <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Music Stores
                  </Typography>
                </Box>

                <TableContainer>
                  <Table>
                    <TableHead sx={{ bgcolor: 'grey.50' }}>
                      <TableRow>
                        <TableCell padding="checkbox">
                          <Checkbox />
                        </TableCell>
                        <TableCell>Store Name</TableCell>
                        <TableCell>Location</TableCell>
                        <TableCell>Total Plays</TableCell>
                        <TableCell>Playlists</TableCell>
                        <TableCell>Tracks</TableCell>
                        <TableCell>Status</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {loading ? (
                        <TableRow>
                          <TableCell colSpan={7} align="center">
                            <Typography variant="body2" sx={{ py: 4 }}>
                              Loading stores...
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ) : tableData.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={7} align="center">
                            <Typography variant="body2" sx={{ py: 4, color: 'text.secondary' }}>
                              No stores found
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ) : (
                        tableData.map((row) => (
                          <TableRow key={row.id} hover>
                            <TableCell padding="checkbox">
                              <Checkbox />
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                {row.distribution}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                {row.store}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {row.plays}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {row.playlists}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {row.tracks}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={row.status}
                                size="small"
                                color={row.status === 'Active' ? 'success' : 'default'}
                                sx={{ fontSize: 12 }}
                              />
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Paper>
            </>
          )}


        </Box>
      </Box>
    </Box>
  );
};

export default MaterialAdminDashboard;
