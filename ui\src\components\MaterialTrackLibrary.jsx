import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useMedia } from '../context/MediaContext';
import { trackService, adminService, playlistService } from '../services/api';
import { audioService } from '../services/audioService';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  IconButton,
  TextField,
  InputAdornment,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
  Alert,
  Checkbox,
  FormControlLabel,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Card,
  CardContent,
  Divider,
  Stack
} from '@mui/material';
import {
  Search as SearchIcon,
  Upload as UploadIcon,
  PlayArrow as PlayArrowIcon,
  Pause as PauseIcon,
  MusicNote as MusicNoteIcon,
  Add as AddIcon,
  FilterList as FilterListIcon,
  CloudUpload as CloudUploadIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as PreviewIcon,
  MoreVert as MoreVertIcon,
  QueueMusic as PlaylistIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import ScheduledPlaylistIndicator from './ScheduledPlaylistIndicator';

const MaterialTrackLibrary = () => {
  const { user, isAdmin, isStore } = useAuth();
  const { playMusic, togglePreview, mediaState } = useMedia();

  const [tracks, setTracks] = useState([]);
  const [playlists, setPlaylists] = useState([]);
  const [filteredTracks, setFilteredTracks] = useState([]);
  const [selectedPlaylist, setSelectedPlaylist] = useState(null);
  const [loading, setLoading] = useState(true);
  const [playlistsLoading, setPlaylistsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterGenre, setFilterGenre] = useState('');
  const [currentTrack, setCurrentTrack] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [uploadForm, setUploadForm] = useState({
    title: '',
    artist: '',
    album: '',
    genre: '',
    duration: '',
    year: new Date().getFullYear().toString(),
    // Compliance fields
    isrcCode: '',
    iswcCode: '',
    composers: [{ name: '', role: 'composer', share: 100 }],
    publishers: [{ name: '', publisherCode: '', copyrightOwner: '', share: 100, territory: 'ZA' }],
    recordLabel: { name: '', catalogNumber: '', releaseDate: '', territory: 'ZA' },
    metadata: { bpm: '', key: '', language: 'en', explicit: false, instrumental: false },
    rights: { mechanicalRights: true, performanceRights: true, synchronizationRights: false, licenseType: 'standard' },
    compliance: { samroRegistered: false, sampraRegistered: false, risaCompliant: true, verificationStatus: 'pending' }
  });
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadError, setUploadError] = useState('');

  // Edit and Delete states
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedTrack, setSelectedTrack] = useState(null);
  const [editForm, setEditForm] = useState({
    title: '',
    artist: '',
    album: '',
    genre: '',
    duration: '',
    // Compliance fields
    isrcCode: '',
    iswcCode: '',
    samroWorkNumber: '',
    sampraArtistNumbers: [''],
    composers: [{ name: '', role: 'composer', share: 100, ipiNumber: '' }],
    publishers: [{ name: '', publisherCode: '', copyrightOwner: '', share: 100, territory: 'ZA', ipiNumber: '' }],
    recordLabel: { name: '', catalogNumber: '', releaseDate: '', territory: 'ZA' },
    performanceRightsSplits: [{ rightsHolderName: '', ipiNumber: '', percentage: 100, role: 'composer' }],
    recordingRightsSplits: [{ artistName: '', sampraNumber: '', percentage: 50, role: 'performer' }],
    compliance: { samroRegistered: false, sampraRegistered: false, risaCompliant: true, verificationStatus: 'pending' }
  });
  const [updating, setUpdating] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadTracks();

    // Subscribe to audio service events
    const unsubscribe = audioService.subscribe((state) => {
      setCurrentTrack(state.currentTrack);
      setIsPlaying(state.isPlaying);
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    filterTracks();
  }, [tracks, searchTerm, filterGenre]);

  const loadTracks = async () => {
    setLoading(true);
    try {
      const response = user.role === 'admin'
        ? await adminService.getAllTracks()
        : await trackService.getAll();

      // Handle different response structures
      let tracksData;
      if (user.role === 'admin') {
        // Admin endpoint returns tracks directly
        tracksData = Array.isArray(response.data) ? response.data : [];
      } else {
        // Regular endpoint returns nested structure with success/data/metadata
        tracksData = Array.isArray(response.data?.data) ? response.data.data : [];
      }

      setTracks(tracksData);
    } catch (error) {
      console.error('Failed to load tracks:', error);
      // Set empty array on error
      setTracks([]);
    } finally {
      setLoading(false);
    }
  };

  const loadPlaylists = async () => {
    console.log('loadPlaylists called with user:', {
      role: user?.role,
      storeId: user?.storeId,
      username: user?.username,
      isAdmin,
      isStore
    });

    setPlaylistsLoading(true);
    try {
      let response;

      // Admin users should see ALL playlists from all stores
      if (user?.role === 'admin') {
        console.log('Loading all playlists for admin user');
        response = await playlistService.getAll();
      } else if (user?.storeId) {
        // Store users see only their store's playlists
        console.log('Loading playlists for store:', user.storeId);
        response = await playlistService.getByStore(user.storeId);
      } else {
        // No store ID and not admin - can't load playlists
        console.log('User has no store ID and is not admin - cannot load playlists');
        setPlaylists([]);
        setPlaylistsLoading(false);
        return;
      }

      console.log('Playlist API response:', response);
      const playlistData = response.data?.data || response.data || [];
      console.log('Loaded playlists:', playlistData.length, 'playlists');
      console.log('Playlist data:', playlistData);
      setPlaylists(Array.isArray(playlistData) ? playlistData : []);
    } catch (error) {
      console.error('Failed to load playlists:', error);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        message: error.message,
        url: error.config?.url
      });
      setPlaylists([]);
    } finally {
      setPlaylistsLoading(false);
    }
  };

  const filterTracks = () => {
    // Ensure tracks is an array before filtering
    let filtered = Array.isArray(tracks) ? tracks : [];

    if (searchTerm) {
      filtered = filtered.filter(track =>
        track.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        track.artist?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        track.album?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (filterGenre) {
      filtered = filtered.filter(track => track.genre === filterGenre);
    }

    setFilteredTracks(filtered);
  };

  const handlePlayTrack = (track) => {
    console.log('TrackLibrary: handlePlayTrack called', {
      track: {
        id: track._id,
        title: track.title,
        artist: track.artist,
        filePath: track.filePath
      },
      currentTrack: currentTrack?._id,
      isPlaying
    });

    if (currentTrack?._id === track._id && isPlaying) {
      // If the same track is playing, pause it
      console.log('TrackLibrary: Pausing current track');
      audioService.pause();
    } else {
      // Set store ID for compliance logging
      if (user?.storeId) {
        console.log('TrackLibrary: Setting store ID for compliance logging', user.storeId);
        audioService.setStoreId(user.storeId);
      }

      // Validate track data before playing
      if (!track.filePath) {
        console.error('TrackLibrary: Track missing filePath', track);
        alert('Cannot play track: Missing audio file');
        return;
      }

      console.log('TrackLibrary: Calling playMusic with track');
      // Use playMusic from MediaContext to ensure proper state management
      // Create a single-track playlist for the selected track
      try {
        playMusic([track], 0, { name: `Playing: ${track.title}` });
      } catch (error) {
        console.error('TrackLibrary: Error calling playMusic:', error);
        alert('Failed to play track. Please try again.');
      }
    }
  };

  const handlePlayPlaylist = (playlist) => {
    if (playlist.tracks && playlist.tracks.length > 0) {
      // Use the media context to play the playlist
      const playlistInfo = {
        _id: playlist._id,
        name: playlist.name
      };
      playMusic(playlist.tracks, 0, playlistInfo);
      setSelectedPlaylist(playlist);
    }
  };

  const handlePlayTrackFromPlaylist = (playlist, trackIndex) => {
    if (playlist.tracks && playlist.tracks.length > trackIndex) {
      // Use the media context to play the playlist starting from the selected track
      const playlistInfo = {
        _id: playlist._id,
        name: playlist.name
      };
      playMusic(playlist.tracks, trackIndex, playlistInfo);
      setSelectedPlaylist(playlist);
    }
  };



  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file && file.type.startsWith('audio/')) {
      setSelectedFile(file);
      setUploadError('');
    } else {
      setUploadError('Please select a valid audio file');
    }
  };

  const handleUpload = async () => {
    if (!selectedFile || !uploadForm.title || !uploadForm.artist) {
      setUploadError('Please fill in all required fields and select a file');
      return;
    }

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('track', selectedFile);
      formData.append('title', uploadForm.title);
      formData.append('artist', uploadForm.artist);
      formData.append('album', uploadForm.album);
      formData.append('genre', uploadForm.genre);
      formData.append('duration', uploadForm.duration || 180);
      formData.append('year', uploadForm.year);

      // Add compliance metadata
      if (uploadForm.isrcCode) formData.append('isrcCode', uploadForm.isrcCode);
      if (uploadForm.iswcCode) formData.append('iswcCode', uploadForm.iswcCode);
      formData.append('composers', JSON.stringify(uploadForm.composers));
      formData.append('publishers', JSON.stringify(uploadForm.publishers));
      formData.append('recordLabel', JSON.stringify(uploadForm.recordLabel));
      formData.append('metadata', JSON.stringify(uploadForm.metadata));
      formData.append('rights', JSON.stringify(uploadForm.rights));
      formData.append('compliance', JSON.stringify(uploadForm.compliance));

      await trackService.upload(formData);

      // Reset form and close dialog
      setUploadForm({
        title: '',
        artist: '',
        album: '',
        genre: '',
        duration: '',
        year: new Date().getFullYear().toString(),
        isrcCode: '',
        iswcCode: '',
        composers: [{ name: '', role: 'composer', share: 100 }],
        publishers: [{ name: '', publisherCode: '', copyrightOwner: '', share: 100, territory: 'ZA' }],
        recordLabel: { name: '', catalogNumber: '', releaseDate: '', territory: 'ZA' },
        metadata: { bpm: '', key: '', language: 'en', explicit: false, instrumental: false },
        rights: { mechanicalRights: true, performanceRights: true, synchronizationRights: false, licenseType: 'standard' },
        compliance: { samroRegistered: false, sampraRegistered: false, risaCompliant: true, verificationStatus: 'pending' }
      });
      setSelectedFile(null);
      setUploadDialogOpen(false);
      setUploadError('');

      // Reload tracks
      loadTracks();
    } catch (error) {
      console.error('Upload failed:', error);
      setUploadError('Upload failed. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Admin functions for edit, delete, preview
  const handleEditTrack = (track) => {
    setSelectedTrack(track);
    setEditForm({
      title: track.title,
      artist: track.artist,
      album: track.album || '',
      genre: track.genre || '',
      duration: track.duration || '',
      // Compliance fields
      isrcCode: track.isrcCode || '',
      iswcCode: track.iswcCode || '',
      samroWorkNumber: track.samroWorkNumber || '',
      sampraArtistNumbers: (track.sampraArtistNumbers && track.sampraArtistNumbers.length > 0) ? track.sampraArtistNumbers : [''],
      composers: track.composers || [{ name: '', role: 'composer', share: 100, ipiNumber: '' }],
      publishers: track.publishers || [{ name: '', publisherCode: '', copyrightOwner: '', share: 100, territory: 'ZA', ipiNumber: '' }],
      recordLabel: track.recordLabel || { name: '', catalogNumber: '', releaseDate: '', territory: 'ZA' },
      performanceRightsSplits: track.performanceRightsSplits || [{ rightsHolderName: '', ipiNumber: '', percentage: 100, role: 'composer' }],
      recordingRightsSplits: track.recordingRightsSplits || [{ artistName: '', sampraNumber: '', percentage: 50, role: 'performer' }],
      compliance: track.compliance || { samroRegistered: false, sampraRegistered: false, risaCompliant: true, verificationStatus: 'pending' }
    });
    setEditDialogOpen(true);
  };

  const handleUpdateTrack = async () => {
    if (!editForm.title || !editForm.artist) {
      alert('Title and artist are required fields');
      return;
    }

    setUpdating(true);
    try {
      console.log('Updating track with data:', {
        id: selectedTrack._id,
        title: editForm.title,
        artist: editForm.artist,
        hasCompliance: !!editForm.compliance,
        hasComposers: !!editForm.composers,
        hasPublishers: !!editForm.publishers
      });

      const response = await adminService.updateTrack(selectedTrack._id, editForm);
      console.log('Track update response:', response);

      setEditDialogOpen(false);
      setSelectedTrack(null);
      loadTracks();

      // Show success message
      alert('Track updated successfully!');
    } catch (error) {
      console.error('Failed to update track:', error);
      const errorMessage = error.response?.data?.error || error.message || 'Failed to update track';
      alert(`Error updating track: ${errorMessage}`);
    } finally {
      setUpdating(false);
    }
  };

  const handleDeleteTrack = (track) => {
    setSelectedTrack(track);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteTrack = async () => {
    setDeleting(true);
    try {
      await adminService.deleteTrack(selectedTrack._id);
      setDeleteDialogOpen(false);
      setSelectedTrack(null);
      loadTracks();
    } catch (error) {
      console.error('Failed to delete track:', error);
    } finally {
      setDeleting(false);
    }
  };

  const handlePreviewTrack = (track) => {
    // Use the MediaContext preview functionality instead of main audio service
    togglePreview(track);
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '--:--';
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const genres = [...new Set((Array.isArray(tracks) ? tracks : []).map(track => track.genre).filter(Boolean))];

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
            Track Library
          </Typography>
          
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {isAdmin && (
            <Button
              variant="contained"
              startIcon={<UploadIcon />}
              onClick={() => setUploadDialogOpen(true)}
              sx={{ bgcolor: 'primary.main' }}
            >
              Upload Track
            </Button>
          )}
        </Box>
      </Box>



      {/* Search and Filter */}
        <Paper sx={{ p: 3, mb: 3, borderRadius: 3 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search tracks, artists, or albums..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: 'text.secondary' }} />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Filter by Genre</InputLabel>
                <Select
                  value={filterGenre}
                  label="Filter by Genre"
                  onChange={(e) => setFilterGenre(e.target.value)}
                  sx={{ borderRadius: 2 }}
                >
                  <MenuItem value="">All Genres</MenuItem>
                  {genres.map(genre => (
                    <MenuItem key={genre} value={genre}>{genre}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FilterListIcon sx={{ color: 'text.secondary' }} />
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  {(Array.isArray(filteredTracks) ? filteredTracks : []).length} tracks
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>

      {/* Tracks Table */}
        <Paper sx={{ borderRadius: 3, overflow: 'hidden' }}>
          {loading ? (
            <LinearProgress />
          ) : (
            <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ bgcolor: 'grey.50' }}>
                  <TableCell sx={{ fontWeight: 600, color: 'black' }}>Track</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'black' }}>Artist</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'black' }}>Album</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'black' }}>Genre</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'black' }}>Duration</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: 'black' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {(Array.isArray(filteredTracks) ? filteredTracks : []).length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} sx={{ textAlign: 'center', py: 8 }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                        <MusicNoteIcon sx={{ fontSize: 48, color: 'text.secondary' }} />
                        <Typography variant="h6" sx={{ color: 'text.secondary' }}>
                          No tracks found
                        </Typography>
                        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                          {searchTerm || filterGenre
                            ? 'Try adjusting your search or filter criteria.'
                            : 'Upload your first track to get started.'}
                        </Typography>
                        {!searchTerm && !filterGenre && isAdmin && (
                          <Button
                            variant="contained"
                            startIcon={<UploadIcon />}
                            onClick={() => setUploadDialogOpen(true)}
                            sx={{ mt: 1 }}
                          >
                            Upload Track
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                ) : (
                  (Array.isArray(filteredTracks) ? filteredTracks : []).map((track) => (
                    <TableRow
                      key={track._id}
                      sx={{
                        '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.02)' },
                        bgcolor: currentTrack?._id === track._id ? 'primary.50' : 'transparent'
                      }}
                    >
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar
                            sx={{
                              width: 40,
                              height: 40,
                              bgcolor: 'primary.main'
                            }}
                          >
                            <MusicNoteIcon />
                          </Avatar>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {track.title}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{track.artist}</Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{track.album || '--'}</Typography>
                      </TableCell>
                      <TableCell>
                        {track.genre && (
                          <Chip
                            label={track.genre}
                            size="small"
                            sx={{ bgcolor: 'grey.100', color: 'black' }}
                          />
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDuration(track.duration)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          {/* Preview/Play Button */}
                          <IconButton
                            onClick={() => handlePreviewTrack(track)}
                            size="small"
                            sx={{
                              bgcolor: mediaState.previewState.currentTrack?._id === track._id && mediaState.previewState.isPlaying ? 'primary.main' : 'grey.100',
                              color: mediaState.previewState.currentTrack?._id === track._id && mediaState.previewState.isPlaying ? 'white' : 'green',
                              '&:hover': {
                                bgcolor: mediaState.previewState.currentTrack?._id === track._id && mediaState.previewState.isPlaying ? 'primary.dark' : 'grey.200'
                              }
                            }}
                          >
                            {mediaState.previewState.currentTrack?._id === track._id && mediaState.previewState.isPlaying ? <PauseIcon /> : <PlayArrowIcon />}
                          </IconButton>

                          {/* Edit Button - Only show for admin users */}
                          {isAdmin && (
                            <IconButton
                              onClick={() => handleEditTrack(track)}
                              size="small"
                              sx={{
                                bgcolor: 'grey.100', color:'blue',
                                '&:hover': { bgcolor: 'grey.200' }
                              }}
                            >
                              <EditIcon />
                            </IconButton>
                          )}

                          {/* Delete Button - Only show for admin users */}
                          {isAdmin && (
                            <IconButton
                              onClick={() => handleDeleteTrack(track)}
                              size="small"
                              sx={{
                                bgcolor: 'grey.100',
                                color: 'error.main',
                                '&:hover': { bgcolor: 'error.50' }
                              }}
                            >
                              <DeleteIcon />
                            </IconButton>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        )}
        </Paper>

      {/* Upload Dialog - Only show for admin users */}
      {isAdmin && (
        <Dialog
          open={uploadDialogOpen}
          onClose={() => setUploadDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
        <DialogTitle>Upload New Track</DialogTitle>
        <DialogContent>
          {uploadError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {uploadError}
            </Alert>
          )}

          <Box sx={{ mt: 2, mb: 3 }}>
            <Button
              variant="outlined"
              component="label"
              startIcon={<CloudUploadIcon />}
              fullWidth
              sx={{ p: 2, borderStyle: 'dashed' }}
            >
              {selectedFile ? selectedFile.name : 'Select Audio File'}
              <input
                type="file"
                hidden
                accept="audio/*"
                onChange={handleFileSelect}
              />
            </Button>
          </Box>

          <Grid container spacing={2}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mb: 1, color: 'primary.main' }}>
                Basic Information
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Track Title *"
                value={uploadForm.title}
                onChange={(e) => setUploadForm({ ...uploadForm, title: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Artist *"
                value={uploadForm.artist}
                onChange={(e) => setUploadForm({ ...uploadForm, artist: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Album"
                value={uploadForm.album}
                onChange={(e) => setUploadForm({ ...uploadForm, album: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Genre"
                value={uploadForm.genre}
                onChange={(e) => setUploadForm({ ...uploadForm, genre: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Duration (seconds)"
                type="number"
                value={uploadForm.duration}
                onChange={(e) => setUploadForm({ ...uploadForm, duration: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Year"
                type="number"
                value={uploadForm.year}
                onChange={(e) => setUploadForm({ ...uploadForm, year: e.target.value })}
              />
            </Grid>

            {/* Compliance Information */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mt: 2, mb: 1, color: 'primary.main' }}>
                Compliance Information
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="ISRC Code"
                value={uploadForm.isrcCode}
                onChange={(e) => setUploadForm({ ...uploadForm, isrcCode: e.target.value })}
                placeholder="CC-XXX-YY-NNNNN"
                helperText="International Standard Recording Code"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="ISWC Code"
                value={uploadForm.iswcCode}
                onChange={(e) => setUploadForm({ ...uploadForm, iswcCode: e.target.value })}
                placeholder="T-NNNNNNNNN-C"
                helperText="International Standard Musical Work Code"
              />
            </Grid>

            {/* Composer Information */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Typography variant="subtitle1" sx={{ mt: 1, mb: 1, fontWeight: 600 }}>
                  Composer Information
                </Typography>
                <Button
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => {
                    const newComposers = [...uploadForm.composers, { name: '', role: 'composer', share: 0 }];
                    setUploadForm({ ...uploadForm, composers: newComposers });
                  }}
                  sx={{ mt: 1 }}
                >
                  Add Composer
                </Button>
              </Box>
            </Grid>

            {uploadForm.composers.map((composer, index) => (
              <React.Fragment key={index}>
                <Grid item xs={12} sm={3}>
                  <TextField
                    fullWidth
                    label={`Composer ${index + 1} Name`}
                    value={composer.name || ''}
                    onChange={(e) => {
                      const newComposers = [...uploadForm.composers];
                      newComposers[index] = { ...newComposers[index], name: e.target.value };
                      setUploadForm({ ...uploadForm, composers: newComposers });
                    }}
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth>
                    <InputLabel>Role</InputLabel>
                    <Select
                      value={composer.role || 'composer'}
                      onChange={(e) => {
                        const newComposers = [...uploadForm.composers];
                        newComposers[index] = { ...newComposers[index], role: e.target.value };
                        setUploadForm({ ...uploadForm, composers: newComposers });
                      }}
                    >
                      <MenuItem value="composer">Composer</MenuItem>
                      <MenuItem value="lyricist">Lyricist</MenuItem>
                      <MenuItem value="arranger">Arranger</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={3}>
                  <TextField
                    fullWidth
                    label="Share (%)"
                    type="number"
                    value={composer.share || 0}
                    onChange={(e) => {
                      const newComposers = [...uploadForm.composers];
                      newComposers[index] = { ...newComposers[index], share: parseInt(e.target.value) || 0 };
                      setUploadForm({ ...uploadForm, composers: newComposers });
                    }}
                    inputProps={{ min: 0, max: 100 }}
                  />
                </Grid>
                <Grid item xs={12} sm={3}>
                  <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
                    {uploadForm.composers.length > 1 && (
                      <IconButton
                        color="error"
                        onClick={() => {
                          const newComposers = uploadForm.composers.filter((_, i) => i !== index);
                          setUploadForm({ ...uploadForm, composers: newComposers });
                        }}
                      >
                        <DeleteIcon />
                      </IconButton>
                    )}
                  </Box>
                </Grid>
              </React.Fragment>
            ))}

            {/* Share validation warning */}
            {(() => {
              const totalShare = uploadForm.composers.reduce((sum, composer) => sum + (composer.share || 0), 0);
              if (totalShare !== 100) {
                return (
                  <Grid item xs={12}>
                    <Alert severity={totalShare > 100 ? "error" : "warning"} sx={{ mt: 1 }}>
                      Total composer shares: {totalShare}%. {totalShare > 100 ? 'Exceeds 100%!' : 'Should equal 100%.'}
                    </Alert>
                  </Grid>
                );
              }
              return null;
            })()}

            {/* Publisher Information */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mt: 1, mb: 1, fontWeight: 600 }}>
                Publisher Information
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Publisher Name"
                value={uploadForm.publishers[0]?.name || ''}
                onChange={(e) => {
                  const newPublishers = [...uploadForm.publishers];
                  newPublishers[0] = { ...newPublishers[0], name: e.target.value };
                  setUploadForm({ ...uploadForm, publishers: newPublishers });
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Publisher Code"
                value={uploadForm.publishers[0]?.publisherCode || ''}
                onChange={(e) => {
                  const newPublishers = [...uploadForm.publishers];
                  newPublishers[0] = { ...newPublishers[0], publisherCode: e.target.value };
                  setUploadForm({ ...uploadForm, publishers: newPublishers });
                }}
              />
            </Grid>

            {/* SAMRO/SAMPRA Registration */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" sx={{ mt: 1, mb: 1, fontWeight: 600 }}>
                SAMRO/SAMPRA Registration
              </Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={uploadForm.compliance?.samroRegistered || false}
                    onChange={(e) => {
                      setUploadForm({
                        ...uploadForm,
                        compliance: { ...uploadForm.compliance, samroRegistered: e.target.checked }
                      });
                    }}
                  />
                }
                label="SAMRO Registered"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={uploadForm.compliance?.sampraRegistered || false}
                    onChange={(e) => {
                      setUploadForm({
                        ...uploadForm,
                        compliance: { ...uploadForm.compliance, sampraRegistered: e.target.checked }
                      });
                    }}
                  />
                }
                label="SAMPRA Registered"
              />
            </Grid>

          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUploadDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleUpload}
            variant="contained"
            disabled={uploading}
          >
            {uploading ? 'Uploading...' : 'Upload'}
          </Button>
        </DialogActions>
        </Dialog>
      )}

      {/* Edit Track Dialog - Only show for admin users */}
      {isAdmin && (
      <Dialog
        open={editDialogOpen}
        onClose={() => setEditDialogOpen(false)}
        maxWidth="md"
        fullWidth
        sx={{ '& .MuiDialog-paper': { maxHeight: '90vh' } }}
      >
        <DialogTitle>Edit Track - {selectedTrack?.title}</DialogTitle>
        <DialogContent sx={{ pb: 1 }}>
          <Grid container spacing={3} sx={{ mt: 1 }}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
                Basic Information
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Track Title *"
                value={editForm.title}
                onChange={(e) => setEditForm({ ...editForm, title: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Artist *"
                value={editForm.artist}
                onChange={(e) => setEditForm({ ...editForm, artist: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Album"
                value={editForm.album}
                onChange={(e) => setEditForm({ ...editForm, album: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Genre"
                value={editForm.genre}
                onChange={(e) => setEditForm({ ...editForm, genre: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Duration (seconds)"
                type="number"
                value={editForm.duration}
                onChange={(e) => setEditForm({ ...editForm, duration: e.target.value })}
              />
            </Grid>

            {/* Compliance Registration */}
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
                Compliance Registration
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={editForm.compliance?.samroRegistered || false}
                    onChange={(e) => setEditForm({
                      ...editForm,
                      compliance: { ...editForm.compliance, samroRegistered: e.target.checked }
                    })}
                  />
                }
                label="SAMRO Registered"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={editForm.compliance?.sampraRegistered || false}
                    onChange={(e) => setEditForm({
                      ...editForm,
                      compliance: { ...editForm.compliance, sampraRegistered: e.target.checked }
                    })}
                  />
                }
                label="SAMPRA Registered"
              />
            </Grid>

            {/* ISRC and ISWC Codes */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="ISRC Code"
                value={editForm.isrcCode}
                onChange={(e) => setEditForm({ ...editForm, isrcCode: e.target.value })}
                placeholder="CC-XXX-YY-NNNNN"
                helperText="International Standard Recording Code"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="ISWC Code"
                value={editForm.iswcCode}
                onChange={(e) => setEditForm({ ...editForm, iswcCode: e.target.value })}
                placeholder="T-NNNNNNNNN-C"
                helperText="International Standard Musical Work Code"
              />
            </Grid>

            {/* SAMRO-specific fields */}
            {editForm.compliance?.samroRegistered && (
              <>
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
                    SAMRO Compliance Information
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="SAMRO Work Number *"
                    value={editForm.samroWorkNumber}
                    onChange={(e) => setEditForm({ ...editForm, samroWorkNumber: e.target.value })}
                    placeholder="SAMRO work registration number"
                    helperText="Required for SAMRO compliance"
                    required
                    error={!editForm.samroWorkNumber}
                  />
                </Grid>

                {/* Composers */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>
                    Composers & IPI Numbers
                  </Typography>
                  {editForm.composers.map((composer, index) => (
                    <Grid container spacing={2} key={index} sx={{ mb: 2 }}>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label={`Composer ${index + 1} Name`}
                          value={composer.name}
                          onChange={(e) => {
                            const newComposers = [...editForm.composers];
                            newComposers[index].name = e.target.value;
                            setEditForm({ ...editForm, composers: newComposers });
                          }}
                          placeholder="Composer name"
                        />
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <TextField
                          fullWidth
                          label="IPI Number"
                          value={composer.ipiNumber || ''}
                          onChange={(e) => {
                            const newComposers = [...editForm.composers];
                            newComposers[index].ipiNumber = e.target.value;
                            setEditForm({ ...editForm, composers: newComposers });
                          }}
                          placeholder="IPI number"
                          helperText="Required for SAMRO"
                          required
                          error={!composer.ipiNumber}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <FormControl fullWidth>
                          <InputLabel>Role</InputLabel>
                          <Select
                            value={composer.role}
                            label="Role"
                            onChange={(e) => {
                              const newComposers = [...editForm.composers];
                              newComposers[index].role = e.target.value;
                              setEditForm({ ...editForm, composers: newComposers });
                            }}
                          >
                            <MenuItem value="composer">Composer</MenuItem>
                            <MenuItem value="lyricist">Lyricist</MenuItem>
                            <MenuItem value="arranger">Arranger</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <TextField
                          fullWidth
                          label="Share %"
                          type="number"
                          value={composer.share}
                          onChange={(e) => {
                            const newComposers = [...editForm.composers];
                            newComposers[index].share = parseInt(e.target.value) || 0;
                            setEditForm({ ...editForm, composers: newComposers });
                          }}
                          inputProps={{ min: 0, max: 100 }}
                        />
                      </Grid>
                      <Grid item xs={12} sm={1}>
                        <Button
                          color="error"
                          onClick={() => {
                            const newComposers = editForm.composers.filter((_, i) => i !== index);
                            setEditForm({ ...editForm, composers: newComposers });
                          }}
                          disabled={editForm.composers.length === 1}
                        >
                          Remove
                        </Button>
                      </Grid>
                    </Grid>
                  ))}
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setEditForm({
                        ...editForm,
                        composers: [...editForm.composers, { name: '', role: 'composer', share: 0, ipiNumber: '' }]
                      });
                    }}
                  >
                    Add Composer
                  </Button>
                </Grid>

                {/* Performance Rights Splits */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>
                    Performance Rights Splits
                  </Typography>
                  {editForm.performanceRightsSplits.map((split, index) => (
                    <Grid container spacing={2} key={index} sx={{ mb: 2 }}>
                      <Grid item xs={12} sm={4}>
                        <TextField
                          fullWidth
                          label="Rights Holder Name"
                          value={split.rightsHolderName}
                          onChange={(e) => {
                            const newSplits = [...editForm.performanceRightsSplits];
                            newSplits[index].rightsHolderName = e.target.value;
                            setEditForm({ ...editForm, performanceRightsSplits: newSplits });
                          }}
                          placeholder="Rights holder name"
                          required
                          error={!split.rightsHolderName}
                        />
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <TextField
                          fullWidth
                          label="IPI Number"
                          value={split.ipiNumber}
                          onChange={(e) => {
                            const newSplits = [...editForm.performanceRightsSplits];
                            newSplits[index].ipiNumber = e.target.value;
                            setEditForm({ ...editForm, performanceRightsSplits: newSplits });
                          }}
                          placeholder="IPI number"
                          required
                          error={!split.ipiNumber}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <TextField
                          fullWidth
                          label="Percentage"
                          type="number"
                          value={split.percentage}
                          onChange={(e) => {
                            const newSplits = [...editForm.performanceRightsSplits];
                            newSplits[index].percentage = parseInt(e.target.value) || 0;
                            setEditForm({ ...editForm, performanceRightsSplits: newSplits });
                          }}
                          inputProps={{ min: 0, max: 100 }}
                          required
                          error={!split.percentage}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <FormControl fullWidth>
                          <InputLabel>Role</InputLabel>
                          <Select
                            value={split.role}
                            label="Role"
                            onChange={(e) => {
                              const newSplits = [...editForm.performanceRightsSplits];
                              newSplits[index].role = e.target.value;
                              setEditForm({ ...editForm, performanceRightsSplits: newSplits });
                            }}
                          >
                            <MenuItem value="composer">Composer</MenuItem>
                            <MenuItem value="lyricist">Lyricist</MenuItem>
                            <MenuItem value="arranger">Arranger</MenuItem>
                            <MenuItem value="publisher">Publisher</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={1}>
                        <Button
                          color="error"
                          onClick={() => {
                            const newSplits = editForm.performanceRightsSplits.filter((_, i) => i !== index);
                            setEditForm({ ...editForm, performanceRightsSplits: newSplits });
                          }}
                          disabled={editForm.performanceRightsSplits.length === 1}
                        >
                          Remove
                        </Button>
                      </Grid>
                    </Grid>
                  ))}
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setEditForm({
                        ...editForm,
                        performanceRightsSplits: [...editForm.performanceRightsSplits, { rightsHolderName: '', ipiNumber: '', percentage: 0, role: 'composer' }]
                      });
                    }}
                  >
                    Add Performance Rights Split
                  </Button>
                </Grid>
              </>
            )}

            {/* SAMPRA-specific fields */}
            {editForm.compliance?.sampraRegistered && (
              <>
                <Grid item xs={12}>
                  <Divider sx={{ my: 2 }} />
                  <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
                    SAMPRA Compliance Information
                  </Typography>
                </Grid>

                {/* SAMPRA Artist Numbers */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>
                    SAMPRA Artist Numbers
                  </Typography>
                  {(editForm.sampraArtistNumbers || ['']).map((artistNumber, index) => (
                    <Grid container spacing={2} key={index} sx={{ mb: 2 }}>
                      <Grid item xs={12} sm={8}>
                        <TextField
                          fullWidth
                          label={`SAMPRA Artist Number ${index + 1}`}
                          value={artistNumber}
                          onChange={(e) => {
                            const currentNumbers = editForm.sampraArtistNumbers || [''];
                            const newNumbers = [...currentNumbers];
                            newNumbers[index] = e.target.value;
                            setEditForm({ ...editForm, sampraArtistNumbers: newNumbers });
                          }}
                          placeholder="SAMPRA registration number"
                          helperText="Required for SAMPRA compliance"
                          required
                          error={!artistNumber}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Button
                          variant="outlined"
                          onClick={() => {
                            const currentNumbers = editForm.sampraArtistNumbers || [''];
                            setEditForm({
                              ...editForm,
                              sampraArtistNumbers: [...currentNumbers, '']
                            });
                          }}
                        >
                          Add Number
                        </Button>
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Button
                          color="error"
                          onClick={() => {
                            const currentNumbers = editForm.sampraArtistNumbers || [''];
                            const newNumbers = currentNumbers.filter((_, i) => i !== index);
                            setEditForm({ ...editForm, sampraArtistNumbers: newNumbers.length > 0 ? newNumbers : [''] });
                          }}
                          disabled={(editForm.sampraArtistNumbers || ['']).length === 1}
                        >
                          Remove
                        </Button>
                      </Grid>
                    </Grid>
                  ))}
                </Grid>

                {/* Recording Rights Splits */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>
                    Recording Rights Splits
                  </Typography>
                  {editForm.recordingRightsSplits.map((split, index) => (
                    <Grid container spacing={2} key={index} sx={{ mb: 2 }}>
                      <Grid item xs={12} sm={3}>
                        <TextField
                          fullWidth
                          label="Artist Name"
                          value={split.artistName}
                          onChange={(e) => {
                            const newSplits = [...editForm.recordingRightsSplits];
                            newSplits[index].artistName = e.target.value;
                            setEditForm({ ...editForm, recordingRightsSplits: newSplits });
                          }}
                          placeholder="Artist name"
                          required
                          error={!split.artistName}
                        />
                      </Grid>
                      <Grid item xs={12} sm={3}>
                        <TextField
                          fullWidth
                          label="SAMPRA Number"
                          value={split.sampraNumber}
                          onChange={(e) => {
                            const newSplits = [...editForm.recordingRightsSplits];
                            newSplits[index].sampraNumber = e.target.value;
                            setEditForm({ ...editForm, recordingRightsSplits: newSplits });
                          }}
                          placeholder="SAMPRA number"
                          required
                          error={!split.sampraNumber}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <TextField
                          fullWidth
                          label="Percentage"
                          type="number"
                          value={split.percentage}
                          onChange={(e) => {
                            const newSplits = [...editForm.recordingRightsSplits];
                            newSplits[index].percentage = parseInt(e.target.value) || 0;
                            setEditForm({ ...editForm, recordingRightsSplits: newSplits });
                          }}
                          inputProps={{ min: 0, max: 100 }}
                          required
                          error={!split.percentage}
                        />
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <FormControl fullWidth>
                          <InputLabel>Role</InputLabel>
                          <Select
                            value={split.role}
                            label="Role"
                            onChange={(e) => {
                              const newSplits = [...editForm.recordingRightsSplits];
                              newSplits[index].role = e.target.value;
                              setEditForm({ ...editForm, recordingRightsSplits: newSplits });
                            }}
                          >
                            <MenuItem value="performer">Performer</MenuItem>
                            <MenuItem value="producer">Producer</MenuItem>
                            <MenuItem value="label">Label</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} sm={2}>
                        <Button
                          color="error"
                          onClick={() => {
                            const newSplits = editForm.recordingRightsSplits.filter((_, i) => i !== index);
                            setEditForm({ ...editForm, recordingRightsSplits: newSplits });
                          }}
                          disabled={editForm.recordingRightsSplits.length === 1}
                        >
                          Remove
                        </Button>
                      </Grid>
                    </Grid>
                  ))}
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setEditForm({
                        ...editForm,
                        recordingRightsSplits: [...editForm.recordingRightsSplits, { artistName: '', sampraNumber: '', percentage: 0, role: 'performer' }]
                      });
                    }}
                  >
                    Add Recording Rights Split
                  </Button>
                </Grid>

                {/* Record Label Information */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>
                    Record Label Information
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Record Label Name *"
                    value={editForm.recordLabel?.name || ''}
                    onChange={(e) => setEditForm({
                      ...editForm,
                      recordLabel: { ...editForm.recordLabel, name: e.target.value }
                    })}
                    placeholder="Record label name"
                    helperText="Required for SAMPRA compliance"
                    required
                    error={!editForm.recordLabel?.name}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Catalog Number"
                    value={editForm.recordLabel?.catalogNumber || ''}
                    onChange={(e) => setEditForm({
                      ...editForm,
                      recordLabel: { ...editForm.recordLabel, catalogNumber: e.target.value }
                    })}
                    placeholder="Catalog number"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Release Date"
                    type="date"
                    value={editForm.recordLabel?.releaseDate ? editForm.recordLabel.releaseDate.split('T')[0] : ''}
                    onChange={(e) => setEditForm({
                      ...editForm,
                      recordLabel: { ...editForm.recordLabel, releaseDate: e.target.value }
                    })}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Territory"
                    value={editForm.recordLabel?.territory || 'ZA'}
                    onChange={(e) => setEditForm({
                      ...editForm,
                      recordLabel: { ...editForm.recordLabel, territory: e.target.value }
                    })}
                    placeholder="Territory code"
                  />
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleUpdateTrack}
            variant="contained"
            disabled={updating}
          >
            {updating ? 'Updating...' : 'Update'}
          </Button>
        </DialogActions>
        </Dialog>
      )}

      {/* Delete Confirmation Dialog - Only show for admin users */}
      {isAdmin && (
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
      >
        <DialogTitle>Delete Track</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{selectedTrack?.title}" by {selectedTrack?.artist}?
          </Typography>
          <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={confirmDeleteTrack}
            variant="contained"
            color="error"
            disabled={deleting}
          >
            {deleting ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
        </Dialog>
      )}

      {/* Scheduled Playlist Indicator */}
      <ScheduledPlaylistIndicator />
    </Box>
  );
};

export default MaterialTrackLibrary;
