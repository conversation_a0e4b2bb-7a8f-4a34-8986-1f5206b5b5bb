const mongoose = require('mongoose');
const VenueLicense = require('../models/VenueLicense.model');
const Store = require('../models/Store.model');
const User = require('../models/User.model');

// Get all venue licenses with filtering and pagination
exports.getVenueLicenses = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      status, 
      licenseType, 
      storeId, 
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    
    if (status) query.status = status;
    if (licenseType) query.licenseType = licenseType;
    if (storeId) query['venue.storeId'] = storeId;
    
    if (search) {
      query.$or = [
        { 'venue.name': { $regex: search, $options: 'i' } },
        { licenseId: { $regex: search, $options: 'i' } },
        { 'samroLicense.licenseNumber': { $regex: search, $options: 'i' } },
        { 'sampraLicense.licenseNumber': { $regex: search, $options: 'i' } }
      ];
    }

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const skip = (page - 1) * limit;

    const [licenses, total] = await Promise.all([
      VenueLicense.find(query)
        .populate('venue.storeId', 'name email address city province')
        .sort(sortOptions)
        .skip(skip)
        .limit(parseInt(limit)),
      VenueLicense.countDocuments(query)
    ]);

    res.json({
      licenses,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total,
        limit: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('Error fetching venue licenses:', error);
    res.status(500).json({ error: 'Failed to fetch venue licenses' });
  }
};

// Get venue license by ID
exports.getVenueLicenseById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const license = await VenueLicense.findById(id)
      .populate('venue.storeId', 'name email address city province phone businessType')
      .populate('auditTrail.performedBy', 'username email role');

    if (!license) {
      return res.status(404).json({ error: 'Venue license not found' });
    }

    res.json(license);

  } catch (error) {
    console.error('Error fetching venue license:', error);
    res.status(500).json({ error: 'Failed to fetch venue license' });
  }
};

// Create new venue license
exports.createVenueLicense = async (req, res) => {
  try {
    const {
      storeId,
      licenseType,
      samroLicense,
      sampraLicense,
      risaCompliance,
      contactPerson,
      businessDetails
    } = req.body;

    // Get store information
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ error: 'Store not found' });
    }

    // Generate unique license ID
    const licenseId = `VL-${Date.now()}-${storeId.slice(-6)}`;

    const venueLicense = await VenueLicense.create({
      licenseId,
      venue: {
        storeId,
        name: store.name,
        address: {
          street: store.address,
          city: store.city,
          province: store.province,
          postalCode: store.postalCode,
          country: store.country || 'South Africa'
        },
        venueType: store.businessType
      },
      licenseType,
      samroLicense,
      sampraLicense,
      risaCompliance,
      contactPerson,
      businessDetails,
      status: 'active',
      auditTrail: [{
        action: 'License Created',
        performedBy: req.user.id,
        details: `Venue license created for ${store.name}`
      }]
    });

    // Update store license information
    const storeUpdate = { licenses: {} };
    
    if (samroLicense && samroLicense.licenseNumber) {
      storeUpdate.licenses.samro = {
        licenseNumber: samroLicense.licenseNumber,
        tariffCode: samroLicense.tariffCode,
        expiryDate: samroLicense.expiryDate,
        isActive: samroLicense.isActive
      };
    }
    
    if (sampraLicense && sampraLicense.licenseNumber) {
      storeUpdate.licenses.sampra = {
        licenseNumber: sampraLicense.licenseNumber,
        tariffCode: sampraLicense.tariffCode,
        expiryDate: sampraLicense.expiryDate,
        isActive: sampraLicense.isActive
      };
    }
    
    if (risaCompliance && risaCompliance.registrationNumber) {
      storeUpdate.licenses.risa = {
        registrationNumber: risaCompliance.registrationNumber,
        isCompliant: risaCompliance.isCompliant
      };
    }

    await Store.findByIdAndUpdate(storeId, storeUpdate);

    const populatedLicense = await VenueLicense.findById(venueLicense._id)
      .populate('venue.storeId', 'name email address city province');

    res.status(201).json(populatedLicense);

  } catch (error) {
    console.error('Error creating venue license:', error);
    res.status(500).json({ error: 'Failed to create venue license' });
  }
};

// Update venue license
exports.updateVenueLicense = async (req, res) => {
  try {
    const { id } = req.params;
    let updateData = { ...req.body };

    const license = await VenueLicense.findById(id);
    if (!license) {
      return res.status(404).json({ error: 'Venue license not found' });
    }

    // Additional organization-specific validation for updates
    // SAMRO staff can only update SAMRO-related fields
    if (req.user.role === 'samro_staff' || req.user.organization === 'SAMRO') {
      // Filter out SAMPRA-specific fields from update
      if (updateData.sampraLicense) {
        delete updateData.sampraLicense;
      }
      if (updateData.licenseType && updateData.licenseType !== 'SAMRO' && updateData.licenseType !== 'combined') {
        return res.status(403).json({ error: 'SAMRO staff cannot change license type to SAMPRA' });
      }
    }

    // SAMPRA staff can only update SAMPRA-related fields
    if (req.user.role === 'sampra_staff' || req.user.organization === 'SAMPRA') {
      // Filter out SAMRO-specific fields from update
      if (updateData.samroLicense) {
        delete updateData.samroLicense;
      }
      if (updateData.licenseType && updateData.licenseType !== 'SAMPRA' && updateData.licenseType !== 'combined') {
        return res.status(403).json({ error: 'SAMPRA staff cannot change license type to SAMRO' });
      }
    }

    // Create a clean copy of the license for audit trail (excluding audit trail to prevent circular reference)
    const licenseForAudit = license.toObject();
    delete licenseForAudit.auditTrail;

    // Create a clean copy of update data for audit trail (excluding any audit trail updates and $push operations)
    const updateDataForAudit = { ...updateData };
    delete updateDataForAudit.$push;
    delete updateDataForAudit.auditTrail; // Remove any direct auditTrail updates to prevent conflicts

    // Remove any auditTrail updates from the main update data to prevent conflicts with $push
    if (updateData.auditTrail) {
      delete updateData.auditTrail;
    }

    // Add audit trail entry using $push
    updateData.$push = {
      auditTrail: {
        action: 'License Updated',
        performedBy: req.user.id,
        details: `License updated by ${req.user.username} (${req.user.role})`,
        oldValues: licenseForAudit,
        newValues: updateDataForAudit,
        timestamp: new Date()
      }
    };

    const updatedLicense = await VenueLicense.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate('venue.storeId', 'name email address city province');

    res.json(updatedLicense);

  } catch (error) {
    console.error('Error updating venue license:', error);
    res.status(500).json({ error: 'Failed to update venue license' });
  }
};

// Delete venue license
exports.deleteVenueLicense = async (req, res) => {
  try {
    const { id } = req.params;

    const license = await VenueLicense.findById(id);
    if (!license) {
      return res.status(404).json({ error: 'Venue license not found' });
    }

    await VenueLicense.findByIdAndDelete(id);

    res.json({ message: 'Venue license deleted successfully' });

  } catch (error) {
    console.error('Error deleting venue license:', error);
    res.status(500).json({ error: 'Failed to delete venue license' });
  }
};

// Get venue license statistics
exports.getVenueLicenseStats = async (req, res) => {
  try {
    const stats = await VenueLicense.aggregate([
      {
        $group: {
          _id: null,
          totalLicenses: { $sum: 1 },
          activeLicenses: {
            $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
          },
          expiredLicenses: {
            $sum: { $cond: [{ $eq: ['$status', 'expired'] }, 1, 0] }
          },
          pendingRenewal: {
            $sum: { $cond: [{ $eq: ['$status', 'pending_renewal'] }, 1, 0] }
          },
          samroOnly: {
            $sum: { $cond: [{ $eq: ['$licenseType', 'samro_only'] }, 1, 0] }
          },
          sampraOnly: {
            $sum: { $cond: [{ $eq: ['$licenseType', 'sampra_only'] }, 1, 0] }
          },
          combined: {
            $sum: { $cond: [{ $eq: ['$licenseType', 'combined'] }, 1, 0] }
          }
        }
      }
    ]);

    // Get licenses expiring soon (within 30 days)
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    const expiringSoon = await VenueLicense.countDocuments({
      $or: [
        { 'samroLicense.expiryDate': { $lte: thirtyDaysFromNow, $gte: new Date() } },
        { 'sampraLicense.expiryDate': { $lte: thirtyDaysFromNow, $gte: new Date() } }
      ],
      status: 'active'
    });

    res.json({
      ...stats[0],
      expiringSoon
    });

  } catch (error) {
    console.error('Error fetching venue license stats:', error);
    res.status(500).json({ error: 'Failed to fetch venue license statistics' });
  }
};

// Get licenses by store ID
exports.getLicensesByStore = async (req, res) => {
  try {
    const { storeId } = req.params;

    const licenses = await VenueLicense.find({ 'venue.storeId': storeId })
      .populate('venue.storeId', 'name email address city province')
      .sort({ createdAt: -1 });

    res.json(licenses);

  } catch (error) {
    console.error('Error fetching store licenses:', error);
    res.status(500).json({ error: 'Failed to fetch store licenses' });
  }
};
